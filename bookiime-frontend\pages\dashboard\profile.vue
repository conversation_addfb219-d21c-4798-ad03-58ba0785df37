<template>
  <div class="max-w-4xl mx-auto space-y-8">
    <!-- Page Header -->
    <div class="bg-white rounded-2xl border border-neutral-200 p-6 sm:p-8 shadow-sm">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6">
        <div>
          <h1 class="text-3xl font-bold text-neutral-900 mb-2">Profile Settings</h1>
          <p class="text-neutral-600 text-lg">Manage your account information and preferences</p>
        </div>
        <div class="flex items-center gap-3">
          <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center shadow-lg">
            <Icon name="lucide:user" class="w-8 h-8 text-white" />
          </div>
        </div>
      </div>
    </div>

    <!-- Profile Form -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Main Profile Information -->
      <div class="lg:col-span-2 space-y-6">
        <!-- Personal Information -->
        <div class="bg-white rounded-2xl border border-neutral-200 p-6 shadow-sm">
          <h2 class="text-xl font-semibold text-neutral-900 mb-6">Personal Information</h2>
          
          <form @submit.prevent="handlePersonalInfoSubmit" class="space-y-6">
            <!-- Profile Picture -->
            <div class="flex items-center space-x-6">
              <div class="relative">
                <div class="w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center shadow-lg">
                  <img 
                    v-if="personalForm.avatar" 
                    :src="personalForm.avatar" 
                    alt="Profile" 
                    class="w-20 h-20 rounded-2xl object-cover"
                  />
                  <Icon v-else name="lucide:user" class="w-10 h-10 text-white" />
                </div>
                <button
                  type="button"
                  @click="triggerAvatarUpload"
                  class="absolute -bottom-2 -right-2 w-8 h-8 bg-white border-2 border-neutral-200 rounded-full flex items-center justify-center hover:bg-neutral-50 transition-colors shadow-sm"
                >
                  <Icon name="lucide:camera" class="w-4 h-4 text-neutral-600" />
                </button>
                <input
                  ref="avatarInput"
                  type="file"
                  accept="image/*"
                  @change="handleAvatarChange"
                  class="hidden"
                />
              </div>
              <div>
                <h3 class="font-medium text-neutral-900">Profile Picture</h3>
                <p class="text-sm text-neutral-500 mt-1">Upload a new profile picture</p>
              </div>
            </div>

            <!-- Name Fields -->
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label for="firstName" class="block text-sm font-medium text-neutral-900 mb-2">
                  First Name *
                </label>
                <input
                  id="firstName"
                  v-model="personalForm.firstName"
                  type="text"
                  required
                  class="w-full px-4 py-3 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="Enter your first name"
                />
              </div>
              <div>
                <label for="lastName" class="block text-sm font-medium text-neutral-900 mb-2">
                  Last Name *
                </label>
                <input
                  id="lastName"
                  v-model="personalForm.lastName"
                  type="text"
                  required
                  class="w-full px-4 py-3 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="Enter your last name"
                />
              </div>
            </div>

            <!-- Email -->
            <div>
              <label for="email" class="block text-sm font-medium text-neutral-900 mb-2">
                Email Address *
              </label>
              <input
                id="email"
                v-model="personalForm.email"
                type="email"
                required
                class="w-full px-4 py-3 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="Enter your email address"
              />
            </div>

            <!-- Phone -->
            <div>
              <label for="phone" class="block text-sm font-medium text-neutral-900 mb-2">
                Phone Number
              </label>
              <input
                id="phone"
                v-model="personalForm.phone"
                type="tel"
                class="w-full px-4 py-3 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="Enter your phone number"
              />
            </div>

            <!-- Bio -->
            <div>
              <label for="bio" class="block text-sm font-medium text-neutral-900 mb-2">
                Bio
              </label>
              <textarea
                id="bio"
                v-model="personalForm.bio"
                rows="4"
                class="w-full px-4 py-3 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
                placeholder="Tell us about yourself..."
              />
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end">
              <button
                type="submit"
                :disabled="personalLoading"
                class="px-6 py-3 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <span v-if="personalLoading" class="flex items-center">
                  <Icon name="lucide:loader-2" class="w-4 h-4 mr-2 animate-spin" />
                  Saving...
                </span>
                <span v-else>Save Changes</span>
              </button>
            </div>
          </form>
        </div>

        <!-- Business Information -->
        <div class="bg-white rounded-2xl border border-neutral-200 p-6 shadow-sm">
          <h2 class="text-xl font-semibold text-neutral-900 mb-6">Business Information</h2>
          
          <form @submit.prevent="handleBusinessInfoSubmit" class="space-y-6">
            <!-- Business Name -->
            <div>
              <label for="businessName" class="block text-sm font-medium text-neutral-900 mb-2">
                Business Name *
              </label>
              <input
                id="businessName"
                v-model="businessForm.businessName"
                type="text"
                required
                class="w-full px-4 py-3 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="Enter your business name"
              />
            </div>

            <!-- Business Type -->
            <div>
              <label for="businessType" class="block text-sm font-medium text-neutral-900 mb-2">
                Business Type *
              </label>
              <select
                id="businessType"
                v-model="businessForm.businessType"
                required
                class="w-full px-4 py-3 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value="">Select business type</option>
                <option value="salon">Hair Salon</option>
                <option value="spa">Spa & Wellness</option>
                <option value="barbershop">Barbershop</option>
                <option value="beauty">Beauty Services</option>
                <option value="fitness">Fitness & Gym</option>
                <option value="healthcare">Healthcare</option>
                <option value="other">Other</option>
              </select>
            </div>

            <!-- Address -->
            <div>
              <label for="address" class="block text-sm font-medium text-neutral-900 mb-2">
                Business Address
              </label>
              <textarea
                id="address"
                v-model="businessForm.address"
                rows="3"
                class="w-full px-4 py-3 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
                placeholder="Enter your business address"
              />
            </div>

            <!-- Website -->
            <div>
              <label for="website" class="block text-sm font-medium text-neutral-900 mb-2">
                Website
              </label>
              <input
                id="website"
                v-model="businessForm.website"
                type="url"
                class="w-full px-4 py-3 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="https://your-website.com"
              />
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end">
              <button
                type="submit"
                :disabled="businessLoading"
                class="px-6 py-3 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <span v-if="businessLoading" class="flex items-center">
                  <Icon name="lucide:loader-2" class="w-4 h-4 mr-2 animate-spin" />
                  Saving...
                </span>
                <span v-else>Save Changes</span>
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Sidebar -->
      <div class="space-y-6">
        <!-- Account Status -->
        <div class="bg-white rounded-2xl border border-neutral-200 p-6 shadow-sm">
          <h3 class="text-lg font-semibold text-neutral-900 mb-4">Account Status</h3>
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <span class="text-sm text-neutral-600">Account Type</span>
              <span class="px-3 py-1 bg-primary-100 text-primary-800 text-sm font-medium rounded-full">
                Pro
              </span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm text-neutral-600">Member Since</span>
              <span class="text-sm text-neutral-900">{{ memberSince }}</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm text-neutral-600">Last Login</span>
              <span class="text-sm text-neutral-900">{{ lastLogin }}</span>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white rounded-2xl border border-neutral-200 p-6 shadow-sm">
          <h3 class="text-lg font-semibold text-neutral-900 mb-4">Quick Actions</h3>
          <div class="space-y-3">
            <button
              @click="changePassword"
              class="w-full flex items-center px-4 py-3 text-left text-neutral-700 hover:bg-neutral-50 rounded-lg transition-colors"
            >
              <Icon name="lucide:key" class="w-5 h-5 mr-3 text-neutral-500" />
              Change Password
            </button>
            <button
              @click="exportData"
              class="w-full flex items-center px-4 py-3 text-left text-neutral-700 hover:bg-neutral-50 rounded-lg transition-colors"
            >
              <Icon name="lucide:download" class="w-5 h-5 mr-3 text-neutral-500" />
              Export Data
            </button>
            <button
              @click="deleteAccount"
              class="w-full flex items-center px-4 py-3 text-left text-red-600 hover:bg-red-50 rounded-lg transition-colors"
            >
              <Icon name="lucide:trash-2" class="w-5 h-5 mr-3 text-red-500" />
              Delete Account
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: 'dashboard'
})

// Composables
const { user } = useUser()

// Form state
const personalForm = ref({
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
  bio: '',
  avatar: null as string | null
})

const businessForm = ref({
  businessName: '',
  businessType: '',
  address: '',
  website: ''
})

const personalLoading = ref(false)
const businessLoading = ref(false)
const avatarInput = ref<HTMLInputElement>()

// Initialize forms with user data
onMounted(() => {
  if (user.value) {
    personalForm.value = {
      firstName: user.value.name?.split(' ')[0] || '',
      lastName: user.value.name?.split(' ').slice(1).join(' ') || '',
      email: user.value.email || '',
      phone: user.value.phone || '',
      bio: user.value.bio || '',
      avatar: user.value.avatar || null
    }
  }
})

// Computed properties
const memberSince = computed(() => {
  return new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long'
  })
})

const lastLogin = computed(() => {
  return new Date().toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  })
})

// Methods
const triggerAvatarUpload = () => {
  avatarInput.value?.click()
}

const handleAvatarChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (file) {
    const reader = new FileReader()
    reader.onload = (e) => {
      personalForm.value.avatar = e.target?.result as string
    }
    reader.readAsDataURL(file)
  }
}

const handlePersonalInfoSubmit = async () => {
  personalLoading.value = true
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const { $toast } = useNuxtApp()
    $toast('Personal information updated successfully!', { type: 'success' })
  } catch (error) {
    const { $toast } = useNuxtApp()
    $toast('Failed to update personal information. Please try again.', { type: 'error' })
  } finally {
    personalLoading.value = false
  }
}

const handleBusinessInfoSubmit = async () => {
  businessLoading.value = true
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const { $toast } = useNuxtApp()
    $toast('Business information updated successfully!', { type: 'success' })
  } catch (error) {
    const { $toast } = useNuxtApp()
    $toast('Failed to update business information. Please try again.', { type: 'error' })
  } finally {
    businessLoading.value = false
  }
}

const changePassword = () => {
  // TODO: Implement password change modal
  const { $toast } = useNuxtApp()
  $toast('Password change feature coming soon!', { type: 'info' })
}

const exportData = () => {
  // TODO: Implement data export
  const { $toast } = useNuxtApp()
  $toast('Data export feature coming soon!', { type: 'info' })
}

const deleteAccount = () => {
  if (confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
    // TODO: Implement account deletion
    const { $toast } = useNuxtApp()
    $toast('Account deletion feature coming soon!', { type: 'info' })
  }
}

// SEO
useHead({
  title: 'Profile Settings - Bookiime Dashboard',
  meta: [
    { name: 'description', content: 'Manage your account information and preferences' }
  ]
})
</script>
