<template>
  <div class="p-6">
    <!-- Page Header -->
    <div class="mb-8">
      <h1 class="text-2xl font-bold text-neutral-900">Settings</h1>
      <p class="text-neutral-600 mt-1">Manage your account and business settings</p>
    </div>

    <!-- Settings Tabs -->
    <div class="bg-white rounded-lg border border-neutral-200">
      <!-- Tab Navigation -->
      <div class="border-b border-neutral-200">
        <nav class="flex space-x-8 px-6">
          <button
            v-for="tab in tabs"
            :key="tab.id"
            @click="activeTab = tab.id"
            class="py-4 px-1 border-b-2 font-medium text-sm transition-colors"
            :class="{
              'border-primary-500 text-primary-600': activeTab === tab.id,
              'border-transparent text-neutral-500 hover:text-neutral-700 hover:border-neutral-300': activeTab !== tab.id
            }"
          >
            <Icon :name="tab.icon" class="w-4 h-4 mr-2 inline" />
            {{ tab.name }}
          </button>
        </nav>
      </div>

      <!-- Tab Content -->
      <div class="p-6">
        <!-- Business Settings -->
        <div v-if="activeTab === 'business'" class="space-y-6">
          <div>
            <h3 class="text-lg font-medium text-neutral-900 mb-4">Business Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label class="block text-sm font-medium text-neutral-700 mb-2">Business Name</label>
                <input
                  v-model="businessSettings.name"
                  type="text"
                  class="w-full border border-neutral-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-neutral-700 mb-2">Subdomain</label>
                <div class="flex">
                  <input
                    v-model="businessSettings.subdomain"
                    type="text"
                    class="flex-1 border border-neutral-300 rounded-l-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  />
                  <span class="inline-flex items-center px-3 py-2 border border-l-0 border-neutral-300 bg-neutral-50 text-neutral-500 rounded-r-lg">
                    .bookiime.com
                  </span>
                </div>
              </div>
              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-neutral-700 mb-2">Description</label>
                <textarea
                  v-model="businessSettings.description"
                  rows="3"
                  class="w-full border border-neutral-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                ></textarea>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-lg font-medium text-neutral-900 mb-4">Contact Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label class="block text-sm font-medium text-neutral-700 mb-2">Phone Number</label>
                <input
                  v-model="businessSettings.phone"
                  type="tel"
                  class="w-full border border-neutral-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-neutral-700 mb-2">Email</label>
                <input
                  v-model="businessSettings.email"
                  type="email"
                  class="w-full border border-neutral-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-neutral-700 mb-2">Address</label>
                <input
                  v-model="businessSettings.address"
                  type="text"
                  class="w-full border border-neutral-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Account Settings -->
        <div v-if="activeTab === 'account'" class="space-y-6">
          <div>
            <h3 class="text-lg font-medium text-neutral-900 mb-4">Account Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label class="block text-sm font-medium text-neutral-700 mb-2">First Name</label>
                <input
                  v-model="accountSettings.firstName"
                  type="text"
                  class="w-full border border-neutral-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-neutral-700 mb-2">Last Name</label>
                <input
                  v-model="accountSettings.lastName"
                  type="text"
                  class="w-full border border-neutral-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-neutral-700 mb-2">Email</label>
                <input
                  v-model="accountSettings.email"
                  type="email"
                  class="w-full border border-neutral-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-neutral-700 mb-2">Phone</label>
                <input
                  v-model="accountSettings.phone"
                  type="tel"
                  class="w-full border border-neutral-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-lg font-medium text-neutral-900 mb-4">Change Password</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label class="block text-sm font-medium text-neutral-700 mb-2">Current Password</label>
                <input
                  v-model="passwordForm.current"
                  type="password"
                  class="w-full border border-neutral-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-neutral-700 mb-2">New Password</label>
                <input
                  v-model="passwordForm.new"
                  type="password"
                  class="w-full border border-neutral-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Notifications -->
        <div v-if="activeTab === 'notifications'" class="space-y-6">
          <div>
            <h3 class="text-lg font-medium text-neutral-900 mb-4">Email Notifications</h3>
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <div>
                  <p class="font-medium text-neutral-900">New Bookings</p>
                  <p class="text-sm text-neutral-600">Get notified when someone books an appointment</p>
                </div>
                <input
                  v-model="notificationSettings.newBookings"
                  type="checkbox"
                  class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-neutral-300 rounded"
                />
              </div>
              <div class="flex items-center justify-between">
                <div>
                  <p class="font-medium text-neutral-900">Booking Reminders</p>
                  <p class="text-sm text-neutral-600">Get reminded about upcoming appointments</p>
                </div>
                <input
                  v-model="notificationSettings.reminders"
                  type="checkbox"
                  class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-neutral-300 rounded"
                />
              </div>
              <div class="flex items-center justify-between">
                <div>
                  <p class="font-medium text-neutral-900">Cancellations</p>
                  <p class="text-sm text-neutral-600">Get notified when bookings are cancelled</p>
                </div>
                <input
                  v-model="notificationSettings.cancellations"
                  type="checkbox"
                  class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-neutral-300 rounded"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Save Button -->
        <div class="flex justify-end pt-6 border-t border-neutral-200">
          <button
            @click="saveSettings"
            :disabled="saving"
            class="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50"
          >
            <Icon v-if="saving" name="lucide:loader-2" class="w-4 h-4 mr-2 animate-spin" />
            <Icon v-else name="lucide:save" class="w-4 h-4 mr-2" />
            {{ saving ? 'Saving...' : 'Save Changes' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: 'dashboard'
})

// Meta tags
useHead({
  title: 'Settings - Bookiime',
  meta: [
    { name: 'description', content: 'Manage your account and business settings' }
  ]
})

const activeTab = ref('business')
const saving = ref(false)

const tabs = [
  { id: 'business', name: 'Business', icon: 'lucide:building' },
  { id: 'account', name: 'Account', icon: 'lucide:user' },
  { id: 'notifications', name: 'Notifications', icon: 'lucide:bell' }
]

const businessSettings = reactive({
  name: 'My Business',
  subdomain: 'mybusiness',
  description: 'A great business providing excellent services',
  phone: '+****************',
  email: '<EMAIL>',
  address: '123 Main St, City, State 12345'
})

const accountSettings = reactive({
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  phone: '+****************'
})

const passwordForm = reactive({
  current: '',
  new: ''
})

const notificationSettings = reactive({
  newBookings: true,
  reminders: true,
  cancellations: false
})

const saveSettings = async () => {
  saving.value = true
  
  try {
    // Replace with actual API calls based on active tab
    if (activeTab.value === 'business') {
      // await $apiFetch('/settings/business', businessSettings)
    } else if (activeTab.value === 'account') {
      // await $apiFetch('/settings/account', accountSettings)
    } else if (activeTab.value === 'notifications') {
      // await $apiFetch('/settings/notifications', notificationSettings)
    }
    
    $toast('Settings saved successfully!', { type: 'success' })
  } catch (error) {
    $toast(getErrorMessage(error), { type: 'error' })
  } finally {
    saving.value = false
  }
}

// Load settings data
onMounted(async () => {
  try {
    // Replace with actual API calls
    const data = await $apiFetch('/settings')
    // Object.assign(businessSettings, data.business)
    // Object.assign(accountSettings, data.account)
    // Object.assign(notificationSettings, data.notifications)
  } catch (error) {
    console.error('Error loading settings:', error)
  }
})
</script>
